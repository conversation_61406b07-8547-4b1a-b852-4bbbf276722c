import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async' as async;
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/message_constants.dart';
import 'package:rolio/widgets/network_error_view.dart';
import 'package:rolio/common/utils/toast_util.dart';

/// 应用异常类
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  AppException(this.message, {String? code, this.originalError})
      : this.code = code ?? ErrorCodes.GENERAL_ERROR;

  @override
  String toString() => 'AppException: $code - $message';
}

/// 网络异常类
class NetworkException extends AppException {
  NetworkException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'NETWORK_ERROR', originalError: originalError);
}

/// 服务器异常类
class ServerException extends AppException {
  ServerException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'SERVER_ERROR', originalError: originalError);
}

/// 认证异常类
class AuthException extends AppException {
  AuthException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'AUTH_ERROR', originalError: originalError);
}

/// 数据异常类
class DataException extends AppException {
  DataException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? 'DATA_ERROR', originalError: originalError);
}

/// WebSocket异常类
class WebSocketException extends AppException {
  WebSocketException(String message, {String? code, dynamic originalError})
      : super(message, code: code ?? ErrorCodes.WS_CONNECTION_FAILED, originalError: originalError);
}

/// 超时异常类
class TimeoutException extends NetworkException {
  /// 超时类型：连接超时、接收超时、发送超时、通用超时
  final String timeoutType;

  TimeoutException(String message, {
    String? code,
    dynamic originalError,
    this.timeoutType = 'general'
  }) : super(message, code: code ?? ErrorCodes.NETWORK_ERROR, originalError: originalError);

  @override
  String toString() => 'TimeoutException($timeoutType): $code - $message';
}

/// 错误信息数据类
class ErrorInfo {
  final String logPrefix;
  final String errorCode;
  final String displayMessage;

  ErrorInfo({
    required this.logPrefix,
    required this.errorCode,
    required this.displayMessage,
  });
}

/// 错误处理工具类
/// 
/// 提供统一的错误处理方法，包括日志记录和用户界面反馈
/// 注意：使用handleException方法作为统一的错误处理入口
class   ErrorHandler {
  /// 错误代码到用户消息的映射
  static const Map<String, String> _errorMessages = {
    // 通用错误
    ErrorCodes.GENERAL_ERROR: MessageConstants.unknownError,
    ErrorCodes.NETWORK_ERROR: MessageConstants.networkError,
    ErrorCodes.SERVER_ERROR: MessageConstants.requestFailed,
    ErrorCodes.AUTH_ERROR: MessageConstants.errorMessage,
    ErrorCodes.BUSINESS_ERROR: MessageConstants.operationFailed,
    ErrorCodes.DATA_NOT_FOUND: MessageConstants.loadFailed,
    ErrorCodes.DATA_PARSE_ERROR: MessageConstants.errorMessage,
    ErrorCodes.DATA_INCONSISTENCY: MessageConstants.errorMessage,
    ErrorCodes.DUPLICATE_REQUEST: MessageConstants.errorMessage,
    ErrorCodes.RATE_LIMIT_ERROR: MessageConstants.errorMessage,
    ErrorCodes.AUTH_IN_PROGRESS: MessageConstants.authInProgressMessage,
    ErrorCodes.REQUEST_TIMEOUT: MessageConstants.requestTimeout,

    // WebSocket相关错误
    ErrorCodes.WS_CONNECTION_FAILED: MessageConstants.connectionFailedMessage,
    ErrorCodes.WS_CONNECTION_LOST: MessageConstants.connectionLostMessage,
    ErrorCodes.WS_MESSAGE_SEND_FAILED: MessageConstants.requestFailed,

    // 聊天模块错误
    ErrorCodes.CHAT_SEND_FAILED: MessageConstants.operationFailed,
    ErrorCodes.CHAT_HISTORY_LOAD_FAILED: MessageConstants.loadHistoryFailedMessage,
    ErrorCodes.CHAT_START_FAILED: MessageConstants.enterChatFailedMessage,
    ErrorCodes.SESSION_SWITCH_FAILED: MessageConstants.errorMessage,

    // 角色模块错误
    ErrorCodes.ROLE_SWITCH_FAILED: MessageConstants.switchRoleFailedMessage,
    ErrorCodes.ROLE_INFO_LOAD_FAILED: MessageConstants.loadFailed,
    ErrorCodes.ROLE_LIST_LOAD_FAILED: MessageConstants.loadRecommendedRolesFailedMessage,
    ErrorCodes.FAVORITE_FAILED: MessageConstants.favoriteFailedMessage,
    ErrorCodes.UNFAVORITE_FAILED: MessageConstants.unfavoriteFailedMessage,
  };
  
  /// 错误处理器映射表
  static final Map<Type, ErrorInfo Function(dynamic)> _errorHandlers = {
    ApiException: (exception) => ErrorInfo(
      logPrefix: 'API错误',
      errorCode: exception.code.toString(),
      displayMessage: exception.message,
    ),
    TimeoutException: (exception) => ErrorInfo(
      logPrefix: '超时错误',
      errorCode: ErrorCodes.NETWORK_ERROR,
      displayMessage: _getTimeoutMessage(exception),
    ),
    NetworkException: (exception) => ErrorInfo(
      logPrefix: '网络错误',
      errorCode: ErrorCodes.NETWORK_ERROR,
      displayMessage: getMessageForErrorCode(ErrorCodes.NETWORK_ERROR),
    ),
    AuthException: (exception) => ErrorInfo(
      logPrefix: '认证错误',
      errorCode: ErrorCodes.AUTH_ERROR,
      displayMessage: getMessageForErrorCode(ErrorCodes.AUTH_ERROR),
    ),
    ServerException: (exception) => ErrorInfo(
      logPrefix: '服务器错误',
      errorCode: ErrorCodes.SERVER_ERROR,
      displayMessage: getMessageForErrorCode(ErrorCodes.SERVER_ERROR),
    ),
    DataException: (exception) => ErrorInfo(
      logPrefix: '数据错误',
      errorCode: ErrorCodes.DATA_PARSE_ERROR,
      displayMessage: getMessageForErrorCode(ErrorCodes.DATA_PARSE_ERROR),
    ),
    WebSocketException: (exception) => ErrorInfo(
      logPrefix: 'WebSocket错误',
      errorCode: ErrorCodes.WS_CONNECTION_FAILED,
      displayMessage: getMessageForErrorCode(ErrorCodes.WS_CONNECTION_FAILED),
    ),
    AppException: (exception) => ErrorInfo(
      logPrefix: '应用错误',
      errorCode: exception.code ?? ErrorCodes.GENERAL_ERROR,
      displayMessage: exception.message,
    ),
  };

  /// 根据错误代码获取用户友好的错误消息
  static String getMessageForErrorCode(String errorCode) {
    return _errorMessages[errorCode] ?? MessageConstants.unknownError;
  }

  /// 获取超时异常的具体消息
  static String _getTimeoutMessage(TimeoutException exception) {
    switch (exception.timeoutType) {
      case 'connection':
        return 'Connection timed out. Please check your network and try again.';
      case 'receive':
        return 'Server response timed out. The server might be busy, please try again later.';
      case 'send':
        return 'Request sending timed out. Please check your network and try again.';
      case 'async':
        return 'Operation timed out. Please try again.';
      default:
        return 'Request timed out. Please try again later.';
    }
  }

  /// 确定异常的错误信息
  static ErrorInfo _determineErrorInfo(dynamic exception, String? customMessage) {
    // 首先检查是否有对应的处理器
    final handler = _errorHandlers[exception.runtimeType];
    if (handler != null) {
      final errorInfo = handler(exception);
      // 如果有自定义消息，使用自定义消息
      if (customMessage != null) {
        return ErrorInfo(
          logPrefix: errorInfo.logPrefix,
          errorCode: errorInfo.errorCode,
          displayMessage: customMessage,
        );
      }
      return errorInfo;
    }

    // 处理Dart原生的TimeoutException
    if (exception is async.TimeoutException) {
      return ErrorInfo(
        logPrefix: '异步超时错误',
        errorCode: ErrorCodes.NETWORK_ERROR,
        displayMessage: customMessage ?? 'Operation timed out. Please try again.',
      );
    }

    // 默认处理
    return ErrorInfo(
      logPrefix: '未知错误',
      errorCode: ErrorCodes.GENERAL_ERROR,
      displayMessage: customMessage ?? MessageConstants.unknownError,
    );
  }

  /// 显示错误消息
  static void _displayErrorMessage(String displayMessage, VoidCallback? onRetry, bool showSnackbar) {
    if (showSnackbar) {
      ToastUtil.error(displayMessage);

      // 如果有重试按钮，显示重试提示
      if (onRetry != null) {
        Future.delayed(const Duration(milliseconds: 3000), () {
          ToastUtil.showToast(
            MessageConstants.retryLoadingMessage,
            type: ToastType.warning,
            duration: const Duration(seconds: 2),
          );
        });
      }
    }
  }


  
  /// 创建应用异常
  /// 
  /// [error] 原始错误对象
  /// [message] 错误消息
  static AppException createAppException(dynamic error, [String? message]) {
    if (error is AppException) {
      return error;
    }
    
    // 处理Dart原生的TimeoutException
    if (error is async.TimeoutException) {
      return TimeoutException(
        message ?? 'Request timed out, please try again later',
        code: ErrorCodes.NETWORK_ERROR,
        originalError: error,
        timeoutType: 'async'
      );
    }
    
    // 根据错误类型创建不同的异常
    if (error.toString().toLowerCase().contains('timeout')) {
      // 细分超时类型
      String timeoutType = 'general';
      if (error.toString().toLowerCase().contains('connection')) {
        timeoutType = 'connection';
      } else if (error.toString().toLowerCase().contains('receive')) {
        timeoutType = 'receive';
      } else if (error.toString().toLowerCase().contains('send')) {
        timeoutType = 'send';
      }
      
      return TimeoutException(
        message ?? 'Connection timed out, please check your network and try again',
        code: ErrorCodes.NETWORK_ERROR,
        originalError: error,
        timeoutType: timeoutType
      );
    } else if (error.toString().toLowerCase().contains('connection') ||
        error.toString().toLowerCase().contains('network')) {
      return NetworkException(message ?? MessageConstants.networkError, code: ErrorCodes.NETWORK_ERROR);
    } else if (error.toString().toLowerCase().contains('unauthorized') ||
               error.toString().toLowerCase().contains('forbidden') ||
               error.toString().toLowerCase().contains('permission')) {
      return AuthException(message ?? MessageConstants.errorMessage, code: ErrorCodes.AUTH_ERROR);
    } else if (error.toString().toLowerCase().contains('not found') ||
               error.toString().toLowerCase().contains('404')) {
      return ServerException(message ?? MessageConstants.loadFailed, code: ErrorCodes.DATA_NOT_FOUND);
    } else if (error.toString().toLowerCase().contains('parse') ||
               error.toString().toLowerCase().contains('format') ||
               error.toString().toLowerCase().contains('type')) {
      return DataException(message ?? MessageConstants.errorMessage, code: ErrorCodes.DATA_PARSE_ERROR);
    } else {
      return AppException(message ?? MessageConstants.unknownError, code: ErrorCodes.GENERAL_ERROR, originalError: error);
    }
  }
  

  

  

  
  /// 处理WebSocket错误
  static void handleWebSocketError(
    dynamic error, {
    String? message,
    bool showSnackbar = true,
    VoidCallback? onRetry,
    String? errorCode,
    bool showFullScreen = false,
    BuildContext? context,
  }) {
    // 创建或转换为WebSocketException
    final wsException = error is WebSocketException
        ? error
        : WebSocketException(
            message ?? 'WebSocket connection error',
            code: errorCode ?? ErrorCodes.WS_CONNECTION_FAILED,
            originalError: error
          );

    // 使用统一的handleException处理错误
    handleException(
      wsException,
      message: message,
      showSnackbar: showSnackbar && !showFullScreen,
      onRetry: onRetry,
    );

    // 显示全屏WebSocket错误页面（如果需要）
    if (showFullScreen && context != null && onRetry != null) {
      showWebSocketErrorPage(
        context,
        onRetry: onRetry,
        errorMessage: message ?? 'WebSocket connection failed'
      );
    }
  }
  

  
  /// 显示成功提示
  static void showSuccess(
    String message, {
    String? title,
    Duration? duration,
  }) {
    ToastUtil.success(message);
  }
  
  /// 显示信息提示
  static void showInfo(
    String message, {
    String? title,
    Duration? duration,
  }) {
    ToastUtil.info(message);
  }
  
  /// 显示全屏网络错误页面
  static void showNetworkErrorPage(
    BuildContext context, {
    required VoidCallback onRetry,
    String errorMessage = 'Network connection error',
    String buttonText = 'Try again',
    bool barrierDismissible = false,
  }) {
    _showFullScreenErrorDialog(
      context,
      onRetry: onRetry,
      errorMessage: errorMessage,
      buttonText: buttonText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// 显示全屏WebSocket错误页面
  static void showWebSocketErrorPage(
    BuildContext context, {
    required VoidCallback onRetry,
    String errorMessage = 'WebSocket connection failed',
    String buttonText = 'Try again',
    bool barrierDismissible = false,
  }) {
    _showFullScreenErrorDialog(
      context,
      onRetry: onRetry,
      errorMessage: errorMessage,
      buttonText: buttonText,
      barrierDismissible: barrierDismissible,
    );
  }
  
  /// 显示全屏错误对话框的内部方法
  static void _showFullScreenErrorDialog(
    BuildContext context, {
    required VoidCallback onRetry,
    required String errorMessage,
    required String buttonText,
    bool barrierDismissible = false,
  }) {
    // 检查是否在对话框中
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
    
    showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.9),
      builder: (context) => WillPopScope(
        onWillPop: () async => barrierDismissible,
        child: NetworkErrorView(
          onRetry: () {
            Navigator.of(context).pop();
            onRetry();
          },
          errorMessage: errorMessage,
          buttonText: buttonText,
        ),
      ),
    );
  }


  
  /// 统一的异常处理入口
  /// 根据异常类型自动选择合适的处理方式
  static void handleException(
    dynamic exception, {
    String? message,
    bool showSnackbar = true,
    VoidCallback? onRetry,
  }) {
    try {
      // 使用新的错误信息确定方法
      final errorInfo = _determineErrorInfo(exception, message);

      // 记录错误日志
      LogUtil.error('${errorInfo.logPrefix} [${errorInfo.errorCode}]: ${errorInfo.displayMessage}, error: $exception');

      // 显示错误消息
      _displayErrorMessage(errorInfo.displayMessage, onRetry, showSnackbar);

    } catch (e) {
      // 确保异常处理本身不会导致应用崩溃
      LogUtil.error('处理异常时发生错误: $e, 原始异常: $exception');

      // 显示简单的错误提示
      if (showSnackbar) {
        ToastUtil.error('An unexpected error occurred');
      }
    }
  }
}