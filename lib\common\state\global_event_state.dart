import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';

/// 全局事件状态管理器
///
/// 使用GetX响应式变量管理全局事件状态，替代EventBus
class GlobalEventState extends GetxController {
  static GlobalEventState get to => Get.find<GlobalEventState>();

  @override
  void onInit() {
    super.onInit();
    LogUtil.info('GlobalEventState初始化完成');
  }

  @override
  void onReady() {
    super.onReady();
    LogUtil.debug('GlobalEventState准备就绪');
  }

  @override
  void onClose() {
    super.onClose();
    LogUtil.info('GlobalEventState资源清理完成');
  }

  // ==================== WebSocket相关事件状态 ====================

  /// WebSocket断开连接事件（保留为事件，因为需要传递错误信息）
  final RxBool websocketDisconnected = false.obs;

  /// AI回复重置事件
  final Rx<Map<String, dynamic>?> aiReplyReset = Rx<Map<String, dynamic>?>(null);

  /// WebSocket错误信息
  final Rx<Map<String, dynamic>?> websocketError = Rx<Map<String, dynamic>?>(null);

  // ==================== 角色相关事件状态 ====================
  
  /// 活跃角色变更事件
  final Rx<Map<String, dynamic>?> activeRoleChanged = Rx<Map<String, dynamic>?>(null);
  
  /// 角色会话绑定事件
  final Rx<Map<String, dynamic>?> roleConversationBound = Rx<Map<String, dynamic>?>(null);

  // ==================== 资源清理事件状态 ====================

  /// 角色资源清理事件
  final Rx<Map<String, dynamic>?> roleResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 角色绑定清理事件
  final Rx<Map<String, dynamic>?> roleBindingCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话资源清理事件
  final Rx<Map<String, dynamic>?> conversationResourceCleanup = Rx<Map<String, dynamic>?>(null);

  /// 会话缓存清理事件
  final Rx<Map<String, dynamic>?> sessionCacheCleanup = Rx<Map<String, dynamic>?>(null);

  // ==================== WebSocket相关事件触发方法 ====================
  
  /// 触发WebSocket断开连接事件
  void triggerWebsocketDisconnected({
    String? error,
    String? state,
    Duration? autoResetDelay = const Duration(milliseconds: 500),
  }) {
    websocketDisconnected.value = true;
    LogUtil.info('触发WebSocket断开连接事件${error != null ? ', 错误: $error' : ''}${state != null ? ', 状态: $state' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        websocketDisconnected.value = false;
      });
    }
  }

  /// 触发AI回复重置事件
  void triggerAiReplyReset({
    String? eventId,
    int? roleId,
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    aiReplyReset.value = {
      'eventId': eventId,
      'roleId': roleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.info('触发AI回复重置事件${eventId != null ? ', 事件ID: $eventId' : ''}${roleId != null ? ', 角色ID: $roleId' : ''}');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        aiReplyReset.value = null;
      });
    }
  }

  /// 触发WebSocket错误事件
  void triggerWebsocketError(Map<String, dynamic> errorData) {
    websocketError.value = errorData;
    LogUtil.info('触发WebSocket错误事件: ${errorData['errorType'] ?? 'UNKNOWN'}');
  }

  // ==================== 消息相关事件触发方法 ====================

  // ==================== 角色相关事件触发方法 ====================

  /// 触发活跃角色变更事件
  void triggerActiveRoleChanged(
    int roleId,
    int? previousRoleId, {
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    activeRoleChanged.value = {
      'roleId': roleId,
      'previousRoleId': previousRoleId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发活跃角色变更事件: roleId=$roleId, previousRoleId=$previousRoleId');

    // 可配置的自动重置状态 - 使用Future.delayed替代Timer
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        activeRoleChanged.value = null;
      });
    }
  }

  /// 触发角色会话绑定事件
  void triggerRoleConversationBound(int roleId, int conversationId) {
    roleConversationBound.value = {
      'role_id': roleId,
      'conversation_id': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色会话绑定事件: roleId=$roleId, conversationId=$conversationId');
  }
  
  /// 触发角色收藏状态变更事件
  void triggerRoleFavoriteChanged(
    int roleId,
    bool isFavorited, {
    Duration? autoResetDelay = const Duration(milliseconds: 300),
  }) {
    roleFavoriteChanged.value = {
      'roleId': roleId,
      'isFavorited': isFavorited,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色收藏状态变更事件: roleId=$roleId, isFavorited=$isFavorited');

    // 可配置的自动重置状态
    if (autoResetDelay != null) {
      Future.delayed(autoResetDelay, () {
        roleFavoriteChanged.value = null;
      });
    }
  }




  // ==================== 资源清理事件触发方法 ====================
  
  /// 触发角色资源清理事件
  void triggerRoleResourceCleanup(int roleId, {int? conversationId, String? reason}) {
    roleResourceCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色资源清理事件: roleId=$roleId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发角色绑定清理事件
  void triggerRoleBindingCleanup(int roleId, {int? conversationId}) {
    roleBindingCleanup.value = {
      'roleId': roleId,
      'conversationId': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发角色绑定清理事件: roleId=$roleId, conversationId=$conversationId');
  }

  /// 触发会话资源清理事件
  void triggerConversationResourceCleanup(int conversationId, {String? reason}) {
    conversationResourceCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话资源清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }

  /// 触发会话缓存清理事件
  void triggerSessionCacheCleanup(int conversationId, {String? reason}) {
    sessionCacheCleanup.value = {
      'conversationId': conversationId,
      'reason': reason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    LogUtil.debug('触发会话缓存清理事件: conversationId=$conversationId${reason != null ? ', 原因: $reason' : ''}');
  }
}
